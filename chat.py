import os
from langchain_groq import ChatGroq
from langchain.chains import Conversation<PERSON>hain
from string import Template
from forecast import get_forecast
from geopy.geocoders import Nominatim
from dotenv import load_dotenv
load_dotenv()

# Global conversation variable
conversation = None

def get_location_by_coordinates(lat, lon):
    geolocator = Nominatim(user_agent="geoapiExercises")
    location = geolocator.reverse((lat, lon), exactly_one=True)
    address = location.raw['address']
    
    region = address.get('state', '')
    if not region:
        region = address.get('region', '')
    if not region:
        region = address.get('county', '')
    
    return region



def get_yield_prediction(lat, lng, crop):
    lat, lng = float(lat), float(lng)
    location = get_location_by_coordinates(lat, lng)

    # Initialize chatbot with API key from environment
    api_key = os.getenv('GROQ_API_KEY')
    if not api_key:
        raise ValueError("GROQ_API_KEY not found in environment variables")

    chatbot = ChatGroq(groq_api_key=api_key)
    global conversation
    conversation = ConversationChain(llm=chatbot)

    firstQuery = Template("""Crop Yield Prediction
                          
    You are an expert on predicting crop yield.
                          
    The farm is located in $location.

    Based on the crop yield values from the last 25 years, you used a LSTM-based model predicts the following crop yield for the upcoming year:
                          
    Yield from previous years: $yieldHistory

    Predicted Yield: $input

    Compare the predicted yield to the yield from previous years (giving more emphasis to past 2-3 years), and to your knowledge of yield of $crop crop in pounds per acre

    And tell if its high or low compared to your knowledge. Also consider the location of the farm. The user has provided all the information you need. Don't ask for more information.

    Respond in friendly manner. If the yield is high, tell the user how they can reach that target or surpass it.
    If the yield is low, tell the user how they can improve their yield.
    """
    )
    time_axis, yieldData = get_forecast(crop, lng, lat)

    yieldHistory = yieldData[:-1]
    input = yieldData[-1]

    firstQuery = firstQuery.substitute(location=location, yieldHistory=yieldHistory, input=input, crop=crop)
    response = conversation.run(firstQuery)

    return {"labels": time_axis.tolist(), "data": yieldData, "response": response}

def chat(user_message):
    global conversation
    if not user_message:
        return {'error': 'No message provided'}

    if conversation is None:
        return {'error': 'Conversation not initialized. Please get a yield prediction first.'}

    try:
        bot_response = conversation.run(user_message)
        return {'response': bot_response}
    except Exception as e:
        return {'error': f'Error in chat: {str(e)}'}