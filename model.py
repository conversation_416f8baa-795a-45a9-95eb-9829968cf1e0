import pandas as pd
import numpy as np
from tensorflow import keras
from sklearn.preprocessing import MinMaxScaler
import pickle
import os

def prepare_data(df, crop, window_size=3):
    """Prepare data from all locations for a specific crop"""
    df_crop = df[df['CROP'] == crop]
    
    all_sequences = []
    all_targets = []
    
    # Collect data from all locations
    for _, row in df_crop.iterrows():
        values = list(row.iloc[3:-2])  # Get yield values
        
        # Create sequences for this location
        for i in range(len(values) - window_size):
            all_sequences.append(values[i:i+window_size])
            all_targets.append(values[i+window_size])
    
    return np.array(all_sequences), np.array(all_targets)

def train_and_save_model(crop, df, window_size=3, epochs=200):
    """Train LSTM model for a specific crop and save it"""
    
    # Prepare data
    X, Y = prepare_data(df, crop, window_size)
    
    # Initialize and fit scaler
    scaler = MinMaxScaler(feature_range=(0, 1))
    X_flat = X.reshape(-1, 1)
    scaler.fit(X_flat)
    
    # Normalize data
    X_normalized = scaler.transform(X.reshape(-1, 1)).reshape(X.shape)
    Y_normalized = scaler.transform(Y.reshape(-1, 1))
    
    # Reshape for LSTM
    X_normalized = X_normalized.reshape(X_normalized.shape[0], X_normalized.shape[1], 1)
    
    # Build LSTM model
    model = keras.Sequential([
        keras.layers.LSTM(50, activation='relu', input_shape=(window_size, 1)),
        keras.layers.Dropout(0.2),  # Add dropout for regularization
        keras.layers.Dense(25, activation='relu'),
        keras.layers.Dense(1)
    ])
    
    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    
    # Train model
    history = model.fit(
        X_normalized, Y_normalized,
        epochs=epochs,
        batch_size=32,
        validation_split=0.2,
        verbose=1
    )
    
    # Create directory for models if it doesn't exist
    os.makedirs('models', exist_ok=True)
    
    # Save model and scaler
    model.save(f'models/lstm_model_{crop}.h5')
    with open(f'models/scaler_{crop}.pkl', 'wb') as f:
        pickle.dump(scaler, f)
    
    return model, scaler, history

# Train models for each crop
if __name__ == "__main__":
    cleaned_df = pd.read_csv('crop_predicitve_analysis/Cleaned_yield_data.csv')
    crops = cleaned_df['CROP'].unique()
    
    for crop in crops:
        print(f"Training model for {crop}...")
        train_and_save_model(crop, cleaned_df)
