import pandas as pd
import numpy as np
from tensorflow import keras
import pickle
import math

# Keep your existing haversine_distance and nearest_point functions

class CropYieldPredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.window_size = 3
        
    def load_model(self, crop):
        """Load pre-trained model and scaler for a specific crop"""
        if crop not in self.models:
            try:
                self.models[crop] = keras.models.load_model(f'models/lstm_model_{crop}.h5')
                with open(f'models/scaler_{crop}.pkl', 'rb') as f:
                    self.scalers[crop] = pickle.load(f)
            except FileNotFoundError:
                raise ValueError(f"No trained model found for crop: {crop}")
        
        return self.models[crop], self.scalers[crop]
    
    def predict_next_value(self, data, crop):
        """Make prediction using pre-trained model"""
        model, scaler = self.load_model(crop)
        
        # Normalize the input data
        data_normalized = scaler.transform(np.array(data).reshape(-1, 1))
        
        # Prepare input sequence
        input_sequence = data_normalized[-self.window_size:].reshape(1, self.window_size, 1)
        
        # Make prediction
        predicted_normalized = model.predict(input_sequence, verbose=0)
        
        # Denormalize
        predicted_value = scaler.inverse_transform(predicted_normalized)
        
        return predicted_value[0][0]
    
    def get_forecast(self, crop, long, lat, df):
        """Get forecast for specific crop and location"""
        df_req_crop = df[df['CROP'] == crop]
        
        nearest_cord = nearest_point(
            df_req_crop['LONGITUDE'], 
            df_req_crop['LATITUDE'], 
            (long, lat)
        )
        
        df_req = df_req_crop[
            (df_req_crop['LONGITUDE'] == nearest_cord[0]) & 
            (df_req_crop['LATITUDE'] == nearest_cord[1])
        ]
        
        required_values = list(df_req.iloc[0, 3:-2])
        next_val = self.predict_next_value(required_values, crop)
        
        extended_data = required_values + [next_val]
        
        # Create time axis
        time_axis = np.array(range(len(extended_data))) + 1987
        time_axis = time_axis.astype(int)
        extended_data = [int(x) for x in extended_data]
        
        return time_axis, extended_data

# Usage
if __name__ == "__main__":
    cleaned_df = pd.read_csv('crop_predicitve_analysis/Cleaned_yield_data.csv')
    predictor = CropYieldPredictor()
    
    # Example prediction
    time_axis, predictions = predictor.get_forecast('WHEAT', 75.5, 25.3, cleaned_df)
