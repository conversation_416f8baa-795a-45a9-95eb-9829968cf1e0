import pandas as pd
import numpy as np
from tensorflow import keras
import pickle
import math

def haversine_distance(lat1, lon1, lat2, lon2):
    """Calculate the great circle distance between two points on the earth (specified in decimal degrees)"""
    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))

    # Radius of earth in kilometers
    r = 6371
    return c * r

def nearest_point(longitudes, latitudes, target_point):
    """Find the nearest point from a list of coordinates to a target point"""
    target_lon, target_lat = target_point
    min_distance = float('inf')
    nearest_coord = None

    for lon, lat in zip(longitudes, latitudes):
        distance = haversine_distance(target_lat, target_lon, lat, lon)
        if distance < min_distance:
            min_distance = distance
            nearest_coord = (lon, lat)

    return nearest_coord

class CropYieldPredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.window_size = 3
        
    def load_model(self, crop):
        """Load pre-trained model and scaler for a specific crop"""
        if crop not in self.models:
            try:
                self.models[crop] = keras.models.load_model(f'models/lstm_model_{crop}.h5')
                with open(f'models/scaler_{crop}.pkl', 'rb') as f:
                    self.scalers[crop] = pickle.load(f)
            except FileNotFoundError:
                raise ValueError(f"No trained model found for crop: {crop}")
        
        return self.models[crop], self.scalers[crop]
    
    def predict_next_value(self, data, crop):
        """Make prediction using pre-trained model"""
        model, scaler = self.load_model(crop)
        
        # Normalize the input data
        data_normalized = scaler.transform(np.array(data).reshape(-1, 1))
        
        # Prepare input sequence
        input_sequence = data_normalized[-self.window_size:].reshape(1, self.window_size, 1)
        
        # Make prediction
        predicted_normalized = model.predict(input_sequence, verbose=0)
        
        # Denormalize
        predicted_value = scaler.inverse_transform(predicted_normalized)
        
        return predicted_value[0][0]
    
    def get_forecast(self, crop, long, lat, df):
        """Get forecast for specific crop and location"""
        df_req_crop = df[df['CROP'] == crop]
        
        nearest_cord = nearest_point(
            df_req_crop['LONGITUDE'], 
            df_req_crop['LATITUDE'], 
            (long, lat)
        )
        
        df_req = df_req_crop[
            (df_req_crop['LONGITUDE'] == nearest_cord[0]) & 
            (df_req_crop['LATITUDE'] == nearest_cord[1])
        ]
        
        required_values = list(df_req.iloc[0, 3:-2])
        next_val = self.predict_next_value(required_values, crop)
        
        extended_data = required_values + [next_val]
        
        # Create time axis
        time_axis = np.array(range(len(extended_data))) + 1987
        time_axis = time_axis.astype(int)
        extended_data = [int(x) for x in extended_data]
        
        return time_axis, extended_data

# Load cleaned data
try:
    cleaned_df = pd.read_csv('Cleaned_data.csv')
except FileNotFoundError:
    try:
        cleaned_df = pd.read_csv('crop_predicitve_analysis/Cleaned_yield_data.csv')
    except FileNotFoundError:
        print("Warning: No cleaned data file found. Please run script.py first to generate Cleaned_data.csv")
        cleaned_df = None

# Legacy function for backward compatibility
def get_forecast(crop, lng, lat):
    """Legacy function for backward compatibility with chat.py"""
    if cleaned_df is None:
        raise FileNotFoundError("No cleaned data available")

    predictor = CropYieldPredictor()
    return predictor.get_forecast(crop, lng, lat, cleaned_df)

# Usage
if __name__ == "__main__":
    if cleaned_df is not None:
        predictor = CropYieldPredictor()

        # Example prediction
        time_axis, predictions = predictor.get_forecast('spring_wheat', -75.5, 45.3, cleaned_df)
        print(f"Time axis: {time_axis}")
        print(f"Predictions: {predictions}")
    else:
        print("Please ensure Cleaned_data.csv exists before running predictions.")
