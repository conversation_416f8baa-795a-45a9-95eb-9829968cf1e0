import streamlit as st
import pandas as pd
import numpy as np
import folium
from streamlit_folium import st_folium
import plotly.graph_objects as go
from forecast import CropYieldPredictor, cleaned_df
import time

# Page configuration
st.set_page_config(
    page_title="Crop Yield Prediction App",
    page_icon="🌾",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for black theme with blue accents
st.markdown("""
<style>
    /* Main app background */
    .stApp {
        background-color: #0e1117;
    }
    
    /* Chat container styling */
    .chat-container {
        background-color: #1a1d24;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        height: 600px;
        overflow-y: auto;
        border: 1px solid #2d3139;
    }
    
    /* Message styling */
    .user-message {
        background-color: #1e88e5;
        color: white;
        padding: 10px 15px;
        border-radius: 18px;
        margin: 10px 0;
        max-width: 70%;
        margin-left: auto;
        text-align: right;
    }
    
    .bot-message {
        background-color: #2d3139;
        color: #e0e0e0;
        padding: 10px 15px;
        border-radius: 18px;
        margin: 10px 0;
        max-width: 70%;
    }
    
    /* Input field styling */
    .stTextInput > div > div > input {
        background-color: #1a1d24;
        color: white;
        border: 1px solid #2d3139;
    }
    
    .stSelectbox > div > div > select {
        background-color: #1a1d24;
        color: white;
        border: 1px solid #2d3139;
    }
    
    /* Button styling */
    .stButton > button {
        background-color: #1e88e5;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s;
    }
    
    .stButton > button:hover {
        background-color: #1565c0;
        transform: translateY(-2px);
    }
    
    /* Header styling */
    h1, h2, h3 {
        color: #ffffff;
    }
    
    /* Map container */
    .map-container {
        border-radius: 10px;
        overflow: hidden;
        border: 1px solid #2d3139;
    }
    
    /* Scrollbar styling */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    ::-webkit-scrollbar-track {
        background: #1a1d24;
    }
    
    ::-webkit-scrollbar-thumb {
        background: #1e88e5;
        border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: #1565c0;
    }
    
    /* Chart container */
    .chart-container {
        background-color: #1a1d24;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
        border: 1px solid #2d3139;
    }
    
    /* Success message */
    .success-tag {
        background-color: #4caf50;
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        display: inline-block;
        margin-bottom: 10px;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'messages' not in st.session_state:
    st.session_state.messages = []
if 'selected_location' not in st.session_state:
    st.session_state.selected_location = None
if 'predictor' not in st.session_state:
    st.session_state.predictor = CropYieldPredictor()

# Header
st.markdown("<h1 style='text-align: center; color: #ffffff;'>🌾 Crop Yield Prediction App</h1>", unsafe_allow_html=True)

# Create two columns
col1, col2 = st.columns([1, 1])

# Left column - Map
with col1:
    st.markdown("<p style='color: #b0b0b0;'>Select a location on the map to get started!</p>", unsafe_allow_html=True)
    
    # Create map centered on Canada
    m = folium.Map(
        location=[56.1304, -106.3468],
        zoom_start=4,
        tiles='OpenStreetMap'
    )
    
    # Add click functionality
    m.add_child(folium.LatLngPopup())
    
    # Display map
    with st.container():
        map_data = st_folium(
            m,
            key="map",
            width=600,
            height=500,
            returned_objects=["last_clicked"]
        )
    
    # Handle map click
    if map_data['last_clicked'] is not None:
        st.session_state.selected_location = (
            map_data['last_clicked']['lng'],
            map_data['last_clicked']['lat']
        )

# Right column - Chat interface
with col2:
    st.markdown("<h3 style='color: #ffffff; margin-bottom: 20px;'>Chat with our Expert!</h3>", unsafe_allow_html=True)
    
    # Chat container
    chat_container = st.container()
    
    with chat_container:
        # Display welcome message if no messages
        if len(st.session_state.messages) == 0:
            st.markdown("""
            <div style='background-color: #1a1d24; border-radius: 10px; padding: 20px; height: 450px; overflow-y: auto; border: 1px solid #2d3139;'>
                <div class='bot-message'>
                    Hi! I am the Crop Yield Prediction Bot. Please select a crop to get started.
                </div>
            </div>
            """, unsafe_allow_html=True)
        else:
            # Display chat messages
            messages_html = "<div style='background-color: #1a1d24; border-radius: 10px; padding: 20px; height: 450px; overflow-y: auto; border: 1px solid #2d3139;'>"
            
            for message in st.session_state.messages:
                if message["role"] == "user":
                    messages_html += f"<div class='user-message'>{message['content']}</div>"
                else:
                    messages_html += f"<div class='bot-message'>{message['content']}</div>"
            
            messages_html += "</div>"
            st.markdown(messages_html, unsafe_allow_html=True)
    
    # Input section
    input_container = st.container()
    
    with input_container:
        # Crop selection
        crop_options = ['Select a crop...'] + list(cleaned_df['CROP'].unique())
        selected_crop = st.selectbox(
            "Crop Type:",
            crop_options,
            key="crop_select"
        )
        
        # Message input
        user_input = st.text_input(
            "Type your message...",
            key="user_input",
            placeholder="Ask about crop yields, predictions, or farming advice..."
        )
        
        # Send button
        if st.button("Send", use_container_width=True):
            if selected_crop != 'Select a crop...' and st.session_state.selected_location:
                # Add user message
                st.session_state.messages.append({
                    "role": "user",
                    "content": f"Show prediction for {selected_crop}"
                })
                
                # Get prediction
                try:
                    time_axis, predictions = st.session_state.predictor.get_forecast(
                        selected_crop,
                        st.session_state.selected_location[0],
                        st.session_state.selected_location[1],
                        cleaned_df
                    )
                    
                    # Create chart
                    fig = go.Figure()
                    
                    # Add historical data
                    fig.add_trace(go.Scatter(
                        x=time_axis[:-1],
                        y=predictions[:-1],
                        mode='lines+markers',
                        name='Historical Yield',
                        line=dict(color='#1e88e5', width=2),
                        marker=dict(size=6)
                    ))
                    
                    # Add prediction point
                    fig.add_trace(go.Scatter(
                        x=[time_axis[-1]],
                        y=[predictions[-1]],
                        mode='markers',
                        name='Predicted Yield',
                        marker=dict(
                            color='#4caf50',
                            size=12,
                            symbol='star'
                        )
                    ))
                    
                    # Update layout
                    fig.update_layout(
                        title=f"{selected_crop} Yield Over Time",
                        xaxis_title="Year",
                        yaxis_title="Yield (kg/ha)",
                        plot_bgcolor='#1a1d24',
                        paper_bgcolor='#1a1d24',
                        font=dict(color='#e0e0e0'),
                        showlegend=True,
                        hovermode='x unified',
                        xaxis=dict(
                            gridcolor='#2d3139',
                            zerolinecolor='#2d3139'
                        ),
                        yaxis=dict(
                            gridcolor='#2d3139',
                            zerolinecolor='#2d3139'
                        )
                    )
                    
                    # Convert figure to HTML
                    chart_html = fig.to_html(include_plotlyjs='cdn', div_id="yield-chart")
                    
                    # Add bot response with chart
                    bot_response = f"""
                    <div class='success-tag'>Selected crop: {selected_crop}</div>
                    <br>
                    Here is the predicted yield for the selected crop.
                    <div class='chart-container'>
                        {chart_html}
                    </div>
                    <br>
                    <strong>Analysis:</strong><br>
                    The predicted yield for {time_axis[-1]} is <strong>{predictions[-1]:.0f} kg/ha</strong>.<br>
                    This represents a {'increase' if predictions[-1] > predictions[-2] else 'decrease'} from the previous year.
                    """
                    
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": bot_response
                    })
                    
                except Exception as e:
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": f"Sorry, I couldn't generate a prediction. Error: {str(e)}"
                    })
                
                # Clear input and rerun
                st.rerun()
                
            elif user_input:
                # Handle general chat
                st.session_state.messages.append({
                    "role": "user",
                    "content": user_input
                })
                
                # Simple bot response
                bot_response = "I can help you predict crop yields! Please select a location on the map and choose a crop type to get started."
                
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": bot_response
                })
                
                st.rerun()

# Footer
st.markdown("""
<div style='text-align: center; margin-top: 50px; color: #666;'>
    <p>Powered by LSTM Neural Networks | Built with Streamlit</p>
</div>
""", unsafe_allow_html=True)
